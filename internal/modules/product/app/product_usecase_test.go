package app

import (
	"context"
	"testing"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockProductRepository is a mock implementation of ProductRepository
type MockProductRepository struct {
	mock.Mock
}

func (m *MockProductRepository) Create(ctx context.Context, product model.Product) error {
	args := m.Called(ctx, product)
	return args.Error(0)
}

func (m *MockProductRepository) Update(ctx context.Context, product model.Product) error {
	args := m.Called(ctx, product)
	return args.Error(0)
}

func (m *MockProductRepository) GetByProp(ctx context.Context, prop string, value string) (*model.Product, error) {
	args := m.Called(ctx, prop, value)
	return args.Get(0).(*model.Product), args.Error(1)
}

func (m *MockProductRepository) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	args := m.Called(ctx, prop, value)
	return args.Int(0), args.Error(1)
}

func (m *MockProductRepository) GetAll(ctx context.Context) ([]model.Product, error) {
	args := m.Called(ctx)
	return args.Get(0).([]model.Product), args.Error(1)
}

func (m *MockProductRepository) Delete(ctx context.Context, id string) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockProductRepository) GetProductsByCategoryCode(ctx context.Context, categoryCode string) ([]model.Product, error) {
	args := m.Called(ctx, categoryCode)
	return args.Get(0).([]model.Product), args.Error(1)
}

func TestProductUsecase_ValidateCode(t *testing.T) {
	mockRepo := new(MockProductRepository)
	usecase := NewProductUsecase(mockRepo)
	ctx := context.Background()

	t.Run("should return nil when code is unique", func(t *testing.T) {
		mockRepo.On("CountByProp", ctx, "code", "UNIQUE-CODE").Return(0, nil)

		err := usecase.ValidateCode(ctx, "UNIQUE-CODE")

		assert.NoError(t, err)
		mockRepo.AssertExpectations(t)
	})

	t.Run("should return error when code already exists", func(t *testing.T) {
		mockRepo.On("CountByProp", ctx, "code", "EXISTING-CODE").Return(1, nil)

		err := usecase.ValidateCode(ctx, "EXISTING-CODE")

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "Product code already exists")
		mockRepo.AssertExpectations(t)
	})
}

func TestProductUsecase_GetProductsByCategoryCode(t *testing.T) {
	mockRepo := new(MockProductRepository)
	usecase := NewProductUsecase(mockRepo)
	ctx := context.Background()

	expectedProducts := []model.Product{
		{
			ID:   "1",
			Name: "Product 1",
			Code: "PROD-1",
		},
		{
			ID:   "2",
			Name: "Product 2",
			Code: "PROD-2",
		},
	}

	t.Run("should return products for valid category code", func(t *testing.T) {
		mockRepo.On("GetProductsByCategoryCode", ctx, "ELECTRONICS").Return(expectedProducts, nil)

		products, err := usecase.GetProductsByCategoryCode(ctx, "ELECTRONICS")

		assert.NoError(t, err)
		assert.Equal(t, expectedProducts, products)
		assert.Len(t, products, 2)
		mockRepo.AssertExpectations(t)
	})
}

func TestProductUsecase_ValidateCommercialName(t *testing.T) {
	mockRepo := new(MockProductRepository)
	usecase := NewProductUsecase(mockRepo)
	ctx := context.Background()

	t.Run("should return nil when commercial name is unique", func(t *testing.T) {
		mockRepo.On("CountByProp", ctx, "commercial_name", "Unique Commercial Name").Return(0, nil)

		err := usecase.ValidateCommercialName(ctx, "Unique Commercial Name")

		assert.NoError(t, err)
		mockRepo.AssertExpectations(t)
	})

	t.Run("should return error when commercial name already exists", func(t *testing.T) {
		mockRepo.On("CountByProp", ctx, "commercial_name", "Existing Commercial Name").Return(1, nil)

		err := usecase.ValidateCommercialName(ctx, "Existing Commercial Name")

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "Product commercial name already exists")
		mockRepo.AssertExpectations(t)
	})
}

func TestProductUsecase_ValidateSKUCode(t *testing.T) {
	mockRepo := new(MockProductRepository)
	usecase := NewProductUsecase(mockRepo)
	ctx := context.Background()

	t.Run("should return nil when SKU code is unique", func(t *testing.T) {
		mockRepo.On("CountByProp", ctx, "sku_code", "UNIQUE-SKU-001").Return(0, nil)

		err := usecase.ValidateSKUCode(ctx, "UNIQUE-SKU-001")

		assert.NoError(t, err)
		mockRepo.AssertExpectations(t)
	})

	t.Run("should return error when SKU code already exists", func(t *testing.T) {
		mockRepo.On("CountByProp", ctx, "sku_code", "EXISTING-SKU-001").Return(1, nil)

		err := usecase.ValidateSKUCode(ctx, "EXISTING-SKU-001")

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "Product SKU code already exists")
		mockRepo.AssertExpectations(t)
	})
}
