package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (p *productPostgreRepo) GetAll(ctx context.Context) ([]model.Product, error) {
	var products []model.Product
	
	err := pg.ExecuteInSchema(ctx, p.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT id, name, image_url, commercial_name, code, sku_code,
				   measurement_unit_id, category_id, brand_id, state,
				   description, can_be_sold, can_be_purchased, cost_price,
				   created_at, updated_at, deleted_at
			FROM products
			WHERE deleted_at IS NULL
			ORDER BY created_at DESC
		`

		rows, err := conn.Query(ctx, query)
		if err != nil {
			return utils.InternalErrorf("failed to get all products", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var product model.Product
			err := rows.Scan(
				&product.ID,
				&product.Name,
				&product.ImageURL,
				&product.CommercialName,
				&product.Code,
				&product.SKUCode,
				&product.MeasurementUnitID,
				&product.CategoryID,
				&product.BrandID,
				&product.State,
				&product.Description,
				&product.CanBeSold,
				&product.CanBePurchased,
				&product.CostPrice,
				&product.CreatedAt,
				&product.UpdatedAt,
				&product.DeletedAt,
			)
			if err != nil {
				return utils.InternalErrorf("failed to scan product", err, nil)
			}
			products = append(products, product)
		}

		if err := rows.Err(); err != nil {
			return utils.InternalErrorf("failed to iterate products", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return products, nil
}
