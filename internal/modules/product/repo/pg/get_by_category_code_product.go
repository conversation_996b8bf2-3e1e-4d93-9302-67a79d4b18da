package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (p *productPostgreRepo) GetProductsByCategoryCode(ctx context.Context, categoryCode string) ([]model.Product, error) {
	var products []model.Product
	
	err := pg.ExecuteInSchema(ctx, p.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT p.id, p.name, p.image_url, p.commercial_name, p.code, p.sku_code,
				   p.measurement_unit_id, p.category_id, p.brand_id, p.state,
				   p.description, p.can_be_sold, p.can_be_purchased, p.cost_price,
				   p.created_at, p.updated_at, p.deleted_at
			FROM products p
			INNER JOIN categories c ON p.category_id = c.id
			WHERE c.code = $1 AND p.deleted_at IS NULL AND c.deleted_at IS NULL
			ORDER BY p.created_at DESC
		`

		rows, err := conn.Query(ctx, query, categoryCode)
		if err != nil {
			return utils.InternalErrorf("failed to get products by category code", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var product model.Product
			err := rows.Scan(
				&product.ID,
				&product.Name,
				&product.ImageURL,
				&product.CommercialName,
				&product.Code,
				&product.SKUCode,
				&product.MeasurementUnitID,
				&product.CategoryID,
				&product.BrandID,
				&product.State,
				&product.Description,
				&product.CanBeSold,
				&product.CanBePurchased,
				&product.CostPrice,
				&product.CreatedAt,
				&product.UpdatedAt,
				&product.DeletedAt,
			)
			if err != nil {
				return utils.InternalErrorf("failed to scan product", err, nil)
			}
			products = append(products, product)
		}

		if err := rows.Err(); err != nil {
			return utils.InternalErrorf("failed to iterate products", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return products, nil
}
