package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (p *productPostgreRepo) Update(ctx context.Context, product model.Product) error {
	return pg.ExecuteInSchema(ctx, p.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			UPDATE products SET
				name = $2,
				image_url = $3,
				commercial_name = $4,
				code = $5,
				sku_code = $6,
				measurement_unit_id = $7,
				category_id = $8,
				brand_id = $9,
				state = $10,
				description = $11,
				can_be_sold = $12,
				can_be_purchased = $13,
				cost_price = $14,
				updated_at = CURRENT_TIMESTAMP
			WHERE id = $1 AND deleted_at IS NULL
		`

		result, err := conn.Exec(ctx, query,
			product.ID,
			product.Name,
			product.ImageURL,
			product.CommercialName,
			product.Code,
			product.SKUCode,
			product.MeasurementUnitID,
			product.CategoryID,
			product.BrandID,
			product.State,
			product.Description,
			product.CanBeSold,
			product.CanBePurchased,
			product.CostPrice,
		)

		if err != nil {
			return utils.InternalErrorf("failed to update product", err, nil)
		}

		if result.RowsAffected() == 0 {
			return model.ProductNotFoundf("Product not found", nil, nil)
		}

		return nil
	})
}
