package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (p *productPostgreRepo) Create(ctx context.Context, product model.Product) error {
	return pg.ExecuteInSchema(ctx, p.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			INSERT INTO products (
				id, name, image_url, commercial_name, code, sku_code,
				measurement_unit_id, category_id, brand_id, state,
				description, can_be_sold, can_be_purchased, cost_price
			)
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
		`

		_, err := conn.Exec(ctx, query,
			product.ID,
			product.Name,
			product.ImageURL,
			product.CommercialName,
			product.Code,
			product.SKUCode,
			product.MeasurementUnitID,
			product.CategoryID,
			product.BrandID,
			product.State,
			product.Description,
			product.CanBeSold,
			product.CanBePurchased,
			product.CostPrice,
		)

		if err != nil {
			return utils.InternalErrorf("failed to create product", err, nil)
		}

		return nil
	})
}
