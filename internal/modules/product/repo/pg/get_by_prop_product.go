package pg

import (
	"context"
	"fmt"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (p *productPostgreRepo) GetByProp(ctx context.Context, prop string, value string) (*model.Product, error) {
	var product model.Product
	
	err := pg.ExecuteInSchema(ctx, p.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := fmt.Sprintf(`
			SELECT id, name, image_url, commercial_name, code, sku_code,
				   measurement_unit_id, category_id, brand_id, state,
				   description, can_be_sold, can_be_purchased, cost_price,
				   created_at, updated_at, deleted_at
			FROM products
			WHERE %s = $1 AND deleted_at IS NULL
		`, prop)

		row := conn.QueryRow(ctx, query, value)
		
		err := row.Scan(
			&product.ID,
			&product.Name,
			&product.ImageURL,
			&product.CommercialName,
			&product.Code,
			&product.SKUCode,
			&product.MeasurementUnitID,
			&product.CategoryID,
			&product.BrandID,
			&product.State,
			&product.Description,
			&product.CanBeSold,
			&product.CanBePurchased,
			&product.CostPrice,
			&product.CreatedAt,
			&product.UpdatedAt,
			&product.DeletedAt,
		)

		if err != nil {
			if err == pgx.ErrNoRows {
				return model.ProductNotFoundf("Product not found", nil, nil)
			}
			return utils.InternalErrorf("failed to get product by prop", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &product, nil
}
